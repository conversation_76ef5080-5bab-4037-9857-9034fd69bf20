import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { GenericSessionService, GenericQueueService } from './';
import { GenericWebSocketGateway } from '../gateways/generic-websocket.gateway';
import { AppException } from '@/common';
import { GENERIC_ERROR_CODES } from '../exceptions';

/**
 * Service để handle shared sessions và real-time collaboration
 */
@Injectable()
export class GenericCollaborationService {
  private readonly logger = new Logger(GenericCollaborationService.name);

  constructor(
    private readonly genericSessionService: GenericSessionService,
    private readonly genericQueueService: GenericQueueService,
    @Inject(forwardRef(() => GenericWebSocketGateway))
    private readonly genericWebSocketGateway: GenericWebSocketGateway,
  ) {}

  /**
   * Share session với users khác
   * @param sessionId Session ID
   * @param userIds Array of user IDs to share with
   * @param permissions Permissions for shared users
   */
  async shareSession(
    sessionId: string,
    userIds: number[],
    permissions: {
      canEdit?: boolean;
      canAddWidgets?: boolean;
      canRemoveWidgets?: boolean;
      canEditLayout?: boolean;
      expiresAt?: Date;
    } = {},
  ): Promise<{ success: boolean; sharedWith: number[] }> {
    try {
      this.logger.log(
        `Sharing session ${sessionId} with users: ${userIds.join(', ')}`,
      );

      // Lấy session hiện tại
      const session =
        await this.genericSessionService.getSessionBySessionId(sessionId);

      // Update session metadata với shared users
      const sharedUsers = session.metadata?.sharedUsers || [];
      const newSharedUsers = userIds.map((userId) => ({
        userId,
        permissions: {
          canEdit: permissions.canEdit || false,
          canAddWidgets: permissions.canAddWidgets || false,
          canRemoveWidgets: permissions.canRemoveWidgets || false,
          canEditLayout: permissions.canEditLayout || false,
        },
        sharedAt: new Date().toISOString(),
        expiresAt: permissions.expiresAt?.toISOString(),
      }));

      // Merge với existing shared users
      const updatedSharedUsers = [
        ...sharedUsers.filter((su: any) => !userIds.includes(su.userId)),
        ...newSharedUsers,
      ];

      await this.genericSessionService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          sharedUsers: updatedSharedUsers,
          isShared: true,
          lastSharedAt: new Date().toISOString(),
        },
      });

      // Broadcast session shared event
      await this.genericQueueService.broadcastToSessionJob(
        sessionId,
        {
          type: 'session_shared',
          payload: {
            sharedWith: userIds,
            permissions,
            sharedBy: session.userId,
          },
        },
        {
          triggeredBy: 'collaboration-service',
        },
      );

      this.logger.log(
        `Session ${sessionId} shared successfully with ${userIds.length} users`,
      );

      return { success: true, sharedWith: userIds };
    } catch (error) {
      this.logger.error(
        `Failed to share session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.SESSION_SHARE_ERROR,
        `Lỗi khi share session ${sessionId}`,
      );
    }
  }

  /**
   * Unshare session với user
   * @param sessionId Session ID
   * @param userId User ID to unshare
   */
  async unshareSession(
    sessionId: string,
    userId: number,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Unsharing session ${sessionId} with user: ${userId}`);

      const session =
        await this.genericSessionService.getSessionBySessionId(sessionId);
      const sharedUsers = session.metadata?.sharedUsers || [];

      // Remove user from shared list
      const updatedSharedUsers = sharedUsers.filter(
        (su: any) => su.userId !== userId,
      );

      await this.genericSessionService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          sharedUsers: updatedSharedUsers,
          isShared: updatedSharedUsers.length > 0,
        },
      });

      // Broadcast session unshared event
      await this.genericQueueService.broadcastToSessionJob(sessionId, {
        type: 'session_unshared',
        payload: {
          unsharedWith: userId,
          unsharedBy: session.userId,
        },
      });

      this.logger.log(
        `Session ${sessionId} unshared successfully with user ${userId}`,
      );

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Failed to unshare session: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.SESSION_UNSHARE_ERROR,
        `Lỗi khi unshare session ${sessionId}`,
      );
    }
  }

  /**
   * Kiểm tra user có permission trên session không
   * @param sessionId Session ID
   * @param userId User ID
   * @param permission Permission to check
   */
  async checkUserPermission(
    sessionId: string,
    userId: number,
    permission:
      | 'canEdit'
      | 'canAddWidgets'
      | 'canRemoveWidgets'
      | 'canEditLayout',
  ): Promise<boolean> {
    try {
      const session =
        await this.genericSessionService.getSessionBySessionId(sessionId);

      // Owner có tất cả permissions
      if (session.userId === userId) {
        return true;
      }

      // Kiểm tra shared permissions
      const sharedUsers = session.metadata?.sharedUsers || [];
      const userShare = sharedUsers.find((su: any) => su.userId === userId);

      if (!userShare) {
        return false;
      }

      // Kiểm tra expiration
      if (userShare.expiresAt && new Date(userShare.expiresAt) < new Date()) {
        return false;
      }

      return userShare.permissions[permission] || false;
    } catch (error) {
      this.logger.error(
        `Failed to check user permission: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Lấy danh sách sessions mà user có access
   * @param userId User ID
   */
  async getUserAccessibleSessions(userId: number): Promise<{
    ownedSessions: any[];
    sharedSessions: any[];
  }> {
    try {
      // Lấy sessions owned by user
      const ownedSessions =
        await this.genericSessionService.getActiveSessionsByUserId(userId);

      // Lấy tất cả active sessions để tìm shared sessions
      const allSessions =
        await this.genericSessionService.getAllActiveSessions();

      const sharedSessions = allSessions.filter((session) => {
        const sharedUsers = session.metadata?.sharedUsers || [];
        return sharedUsers.some((su: any) => {
          if (su.userId !== userId) return false;

          // Kiểm tra expiration
          if (su.expiresAt && new Date(su.expiresAt) < new Date()) {
            return false;
          }

          return true;
        });
      });

      return { ownedSessions, sharedSessions };
    } catch (error) {
      this.logger.error(
        `Failed to get user accessible sessions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        GENERIC_ERROR_CODES.GET_ACCESSIBLE_SESSIONS_ERROR,
        `Lỗi khi lấy sessions của user ${userId}`,
      );
    }
  }

  /**
   * Broadcast user activity trong session
   * @param sessionId Session ID
   * @param userId User ID
   * @param activity Activity data
   */
  async broadcastUserActivity(
    sessionId: string,
    userId: number,
    activity: {
      type: 'cursor_move' | 'widget_select' | 'widget_edit' | 'layout_change';
      data: any;
    },
  ): Promise<void> {
    try {
      // Broadcast activity to other users in session
      this.genericWebSocketGateway.broadcastToSession(sessionId, {
        type: 'user_activity',
        payload: {
          userId,
          activity,
          timestamp: new Date().toISOString(),
        },
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.debug(
        `User activity broadcasted: ${userId} - ${activity.type} in session ${sessionId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to broadcast user activity: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Lock widget để prevent concurrent editing
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @param userId User ID
   * @param lockDuration Lock duration in seconds
   */
  async lockWidget(
    sessionId: string,
    widgetId: string,
    userId: number,
    lockDuration: number = 30,
  ): Promise<{ success: boolean; lockId?: string }> {
    try {
      const session =
        await this.genericSessionService.getSessionBySessionId(sessionId);
      const locks = session.metadata?.widgetLocks || {};

      // Kiểm tra widget đã bị lock chưa
      const existingLock = locks[widgetId];
      if (existingLock && existingLock.expiresAt > new Date().toISOString()) {
        if (existingLock.userId !== userId) {
          return { success: false };
        }
      }

      // Tạo lock mới
      const lockId = `lock-${widgetId}-${userId}-${Date.now()}`;
      const expiresAt = new Date(
        Date.now() + lockDuration * 1000,
      ).toISOString();

      locks[widgetId] = {
        lockId,
        userId,
        lockedAt: new Date().toISOString(),
        expiresAt,
      };

      await this.genericSessionService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          widgetLocks: locks,
        },
      });

      // Broadcast widget locked event
      this.genericWebSocketGateway.broadcastToSession(sessionId, {
        type: 'widget_locked',
        payload: {
          widgetId,
          userId,
          lockId,
          expiresAt,
        },
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.debug(
        `Widget locked: ${widgetId} by user ${userId} in session ${sessionId}`,
      );

      return { success: true, lockId };
    } catch (error) {
      this.logger.error(`Failed to lock widget: ${error.message}`, error.stack);
      return { success: false };
    }
  }

  /**
   * Unlock widget
   * @param sessionId Session ID
   * @param widgetId Widget ID
   * @param userId User ID
   */
  async unlockWidget(
    sessionId: string,
    widgetId: string,
    userId: number,
  ): Promise<{ success: boolean }> {
    try {
      const session =
        await this.genericSessionService.getSessionBySessionId(sessionId);
      const locks = session.metadata?.widgetLocks || {};

      const existingLock = locks[widgetId];
      if (!existingLock || existingLock.userId !== userId) {
        return { success: false };
      }

      // Remove lock
      delete locks[widgetId];

      await this.genericSessionService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          widgetLocks: locks,
        },
      });

      // Broadcast widget unlocked event
      this.genericWebSocketGateway.broadcastToSession(sessionId, {
        type: 'widget_unlocked',
        payload: {
          widgetId,
          userId,
        },
        sessionId,
        timestamp: new Date().toISOString(),
      });

      this.logger.debug(
        `Widget unlocked: ${widgetId} by user ${userId} in session ${sessionId}`,
      );

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Failed to unlock widget: ${error.message}`,
        error.stack,
      );
      return { success: false };
    }
  }
}
