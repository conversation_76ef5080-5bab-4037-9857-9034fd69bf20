// Export main business DTOs
export * from './query-product.dto';
export * from './price.dto';

// Export new DTOs structure - Base and Create DTOs
export * from './base';
// Export individual create DTOs

// Export request DTOs

// Export digital classification DTO
export * from './digital-classification.dto';

// Export new Customer Product DTOs
export * from './customer-product/simple-create-customer-product.dto';
export * from './customer-product/simple-customer-product-response.dto';
export * from './customer-product/bulk-delete-customer-product.dto';

// Export new Physical Product DTOs
export * from './physical-product/complete-update-physical-product.dto';
export * from './physical-product/complete-physical-product-response.dto';
export * from './physical-product/physical-product-variant-response.dto';

// Export simple create product DTO
export * from './simple-create-product.dto';

// Export customer product DTOs
export * from './customer-product';

// Export entity has media DTOs
export * from './entity-has-media';

// Export digital product DTOs - with explicit re-exports to avoid conflicts
export {
  CreateDigitalProductDto,
  UpdateDigitalProductDto,
  QueryDigitalProductDto,
  DigitalProductResponseDto,
  DigitalProductListResponseDto,
  BulkDeleteDigitalProductDto,
  BulkDeleteDigitalProductResponseDto,
} from './digital-product';

// Export response DTOs with different names to avoid conflicts
export { ProductResponseDto as TypeSafeProductResponseDto } from './response';

export * from './custom-field-metadata.dto';
export * from './bulk-delete-product.dto';
export * from './bulk-delete-product-response.dto';
export * from './bulk-delete-custom-field.dto';
export * from './bulk-delete-custom-field-response.dto';
export * from './product-inventory.dto';

export * from './create-custom-field.dto';

// Tránh xung đột tên với product-response.dto
import { CustomFieldResponseDto } from './custom-field-response.dto';
export { CustomFieldResponseDto };

export * from './query-custom-field.dto';
export * from './custom-field-list-response.dto';
export * from './custom-field-detail-response.dto';
export * from './update-custom-field.dto';
export * from './component-response.dto';
export * from './create-custom-field-swagger.dto';
export * from './update-custom-field-swagger.dto';

// User Convert
export * from './user-convert-response.dto';
export * from './query-user-convert.dto';

// User Convert Customer
export * from './create-user-convert-customer.dto';
export * from './create-bulk-user-convert-customer.dto';
export * from './bulk-user-convert-customer-response.dto';
export * from './simple-custom-field.dto';
export * from './update-user-convert-customer.dto';
export * from './merge-user-convert-customer.dto';
export * from './user-convert-customer-response.dto';
export * from './query-user-convert-customer.dto';
export * from './bulk-delete-user-convert-customer.dto';
export * from './bulk-delete-user-convert-customer-response.dto';

// User Convert Customer Merge Recommendations
export * from './create-merge-recommendation.dto';
export * from './query-merge-recommendation.dto';
export * from './merge-recommendation-response.dto';
export * from './bulk-delete-merge-recommendation.dto';

// Individual update DTOs
export * from './update-customer-basic-info.dto';
export * from './update-customer-custom-fields.dto';
export * from './update-customer-social-links.dto';

export * from './metadata-field.dto';

// Customer Social
export * from './customer-facebook.dto';
export * from './customer-web.dto';
export * from './update-customer-social.dto';
export * from './update-social-links.dto';

// Report
export * from './report';

// User Order
export * from './user-order-response.dto';
export * from './query-user-order.dto';
export * from './user-order-status.dto';
export * from './create-user-order.dto';
export * from './tracking-response.dto';
export * from './print-order-response.dto';
export * from './print-order-query.dto';
export * from './bulk-delete-user-order.dto';
export * from './bulk-delete-user-order-response.dto';

// Draft Order DTOs
export * from './create-draft-order.dto';
export * from './draft-order-preview.dto';
export * from './confirm-draft-order.dto';

// User Address
export * from './user-address.dto';
export * from './create-user-address-v1.dto';
export * from './user-shop-address.dto';
export * from './user-shop-address-v2.dto';

// GHTK
export * from './ghtk';

// GHN
export * from './ghn';
export * from './ghn-address';

// Shipping Management
export * from './track-order.dto';
export * from './cancel-order.dto';
export * from './return-order.dto';
export * from './print-order.dto';
export * from './calculate-shipping-fee.dto';
export * from './unified-shipping.dto';

// Webhook DTOs
export * from './webhook-sepayhub-request.dto';
