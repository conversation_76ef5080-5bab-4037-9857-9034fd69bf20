import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import {
  GenericCollaborationService,
  GenericRecoveryService,
} from '../services';
import { GenericSessionGuard } from '../guards/generic-session.guard';
import { ApiResponseDto } from '@/common/response';

/**
 * Controller cho Generic Collaboration Features
 * Quản lý shared sessions, permissions, và real-time collaboration
 */
@ApiTags('Generic Collaboration')
@Controller('generic/collaboration')
@UseGuards(GenericSessionGuard)
@ApiBearerAuth()
export class GenericCollaborationController {
  private readonly logger = new Logger(GenericCollaborationController.name);

  constructor(
    private readonly genericCollaborationService: GenericCollaborationService,
    private readonly genericRecoveryService: GenericRecoveryService,
  ) {}

  /**
   * Share session với users khác
   */
  @Post('sessions/:sessionId/share')
  @ApiOperation({
    summary: 'Share session với users',
    description: 'Share session với users khác và set permissions',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiResponse({
    status: 200,
    description: 'Session đã được share thành công',
  })
  async shareSession(
    @Param('sessionId') sessionId: string,
    @Body()
    body: {
      userIds: number[];
      permissions?: {
        canEdit?: boolean;
        canAddWidgets?: boolean;
        canRemoveWidgets?: boolean;
        canEditLayout?: boolean;
        expiresAt?: string;
      };
    },
  ): Promise<ApiResponseDto<{ sharedWith: number[] }>> {
    try {
      const result = await this.genericCollaborationService.shareSession(
        sessionId,
        body.userIds,
        {
          ...body.permissions,
          expiresAt: body.permissions?.expiresAt
            ? new Date(body.permissions.expiresAt)
            : undefined,
        },
      );

      this.logger.log(
        `Session ${sessionId} shared with ${body.userIds.length} users`,
      );

      return ApiResponseDto.success(result, 'Session đã được share thành công');
    } catch (error) {
      this.logger.error(`Error sharing session: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Unshare session với user
   */
  @Delete('sessions/:sessionId/share/:userId')
  @ApiOperation({
    summary: 'Unshare session với user',
    description: 'Remove user access từ shared session',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to unshare',
    example: 123,
  })
  @ApiResponse({
    status: 200,
    description: 'Session đã được unshare thành công',
  })
  async unshareSession(
    @Param('sessionId') sessionId: string,
    @Param('userId') userId: number,
  ): Promise<ApiResponseDto<any>> {
    try {
      const result = await this.genericCollaborationService.unshareSession(
        sessionId,
        userId,
      );

      this.logger.log(`Session ${sessionId} unshared with user ${userId}`);

      return ApiResponseDto.success(
        result,
        'Session đã được unshare thành công',
      );
    } catch (error) {
      this.logger.error(
        `Error unsharing session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách sessions mà user có access
   */
  @Get('my-sessions')
  @ApiOperation({
    summary: 'Lấy sessions của user',
    description: 'Lấy danh sách sessions owned và shared với user',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách sessions',
  })
  async getMyAccessibleSessions(
    @Request() req: any,
  ): Promise<ApiResponseDto<{ ownedSessions: any[]; sharedSessions: any[] }>> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new Error('User ID not found in request');
      }

      const result =
        await this.genericCollaborationService.getUserAccessibleSessions(
          userId,
        );

      return ApiResponseDto.success(
        result,
        'Lấy danh sách sessions thành công',
      );
    } catch (error) {
      this.logger.error(
        `Error getting accessible sessions: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra user permission trên session
   */
  @Get('sessions/:sessionId/permissions')
  @ApiOperation({
    summary: 'Kiểm tra permissions',
    description: 'Kiểm tra user permissions trên session',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiResponse({
    status: 200,
    description: 'User permissions',
  })
  async checkUserPermissions(
    @Param('sessionId') sessionId: string,
    @Request() req: any,
  ): Promise<
    ApiResponseDto<{
      canEdit: boolean;
      canAddWidgets: boolean;
      canRemoveWidgets: boolean;
      canEditLayout: boolean;
    }>
  > {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new Error('User ID not found in request');
      }

      const [canEdit, canAddWidgets, canRemoveWidgets, canEditLayout] =
        await Promise.all([
          this.genericCollaborationService.checkUserPermission(
            sessionId,
            userId,
            'canEdit',
          ),
          this.genericCollaborationService.checkUserPermission(
            sessionId,
            userId,
            'canAddWidgets',
          ),
          this.genericCollaborationService.checkUserPermission(
            sessionId,
            userId,
            'canRemoveWidgets',
          ),
          this.genericCollaborationService.checkUserPermission(
            sessionId,
            userId,
            'canEditLayout',
          ),
        ]);

      const result = {
        canEdit,
        canAddWidgets,
        canRemoveWidgets,
        canEditLayout,
      };

      return ApiResponseDto.success(result, 'Kiểm tra permissions thành công');
    } catch (error) {
      this.logger.error(
        `Error checking permissions: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lock widget để edit
   */
  @Post('sessions/:sessionId/widgets/:widgetId/lock')
  @ApiOperation({
    summary: 'Lock widget',
    description: 'Lock widget để prevent concurrent editing',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiParam({
    name: 'widgetId',
    description: 'Widget ID',
    example: 'widget-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Widget đã được lock',
  })
  async lockWidget(
    @Param('sessionId') sessionId: string,
    @Param('widgetId') widgetId: string,
    @Body() body: { lockDuration?: number },
    @Request() req: any,
  ): Promise<ApiResponseDto<{ lockId?: string }>> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new Error('User ID not found in request');
      }

      const result = await this.genericCollaborationService.lockWidget(
        sessionId,
        widgetId,
        userId,
        body.lockDuration || 30,
      );

      this.logger.log(
        `Widget ${widgetId} locked by user ${userId} in session ${sessionId}`,
      );

      return ApiResponseDto.success(
        result,
        result.success
          ? 'Widget đã được lock thành công'
          : 'Widget đã bị lock bởi user khác',
      );
    } catch (error) {
      this.logger.error(`Error locking widget: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Unlock widget
   */
  @Delete('sessions/:sessionId/widgets/:widgetId/lock')
  @ApiOperation({
    summary: 'Unlock widget',
    description: 'Unlock widget sau khi edit xong',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiParam({
    name: 'widgetId',
    description: 'Widget ID',
    example: 'widget-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Widget đã được unlock',
  })
  async unlockWidget(
    @Param('sessionId') sessionId: string,
    @Param('widgetId') widgetId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new Error('User ID not found in request');
      }

      const result = await this.genericCollaborationService.unlockWidget(
        sessionId,
        widgetId,
        userId,
      );

      this.logger.log(
        `Widget ${widgetId} unlocked by user ${userId} in session ${sessionId}`,
      );

      return ApiResponseDto.success(result, 'Widget đã được unlock thành công');
    } catch (error) {
      this.logger.error(
        `Error unlocking widget: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Recover session state
   */
  @Post('sessions/:sessionId/recover')
  @ApiOperation({
    summary: 'Recover session state',
    description: 'Khôi phục session state cho client reconnection',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiResponse({
    status: 200,
    description: 'Session state đã được khôi phục',
  })
  async recoverSessionState(@Param('sessionId') sessionId: string): Promise<
    ApiResponseDto<{
      session: any;
      widgets: any[];
      layout: any;
      isRecovered: boolean;
    }>
  > {
    try {
      const result =
        await this.genericRecoveryService.recoverSessionState(sessionId);

      this.logger.log(`Session state recovered: ${sessionId}`);

      return ApiResponseDto.success(
        result,
        'Session state đã được khôi phục thành công',
      );
    } catch (error) {
      this.logger.error(
        `Error recovering session state: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Backup session state
   */
  @Post('sessions/:sessionId/backup')
  @ApiOperation({
    summary: 'Backup session state',
    description: 'Backup session state to persistent storage',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID',
    example: 'session-123-abc',
  })
  @ApiResponse({
    status: 200,
    description: 'Session state đã được backup',
  })
  async backupSessionState(
    @Param('sessionId') sessionId: string,
  ): Promise<ApiResponseDto<{ backupId: string }>> {
    try {
      const result =
        await this.genericRecoveryService.backupSessionState(sessionId);

      this.logger.log(
        `Session state backed up: ${sessionId} - ${result.backupId}`,
      );

      return ApiResponseDto.success(
        result,
        'Session state đã được backup thành công',
      );
    } catch (error) {
      this.logger.error(
        `Error backing up session state: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
