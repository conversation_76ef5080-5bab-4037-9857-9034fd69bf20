import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ScheduleModule } from '@nestjs/schedule';
import { GenericAdminModule } from './admin/generic-admin.module';
import { GenericUserModule } from './user/generic-user.module';
import {
  GenericPage,
  GenericPageSubmission,
  GenericPageTemplate,
  GenericPageTemplateTag,
  GenericSession,
  GenericSessionWidget,
  GenericSessionLayout,
} from './entities';
import {
  GenericPageRepository,
  GenericPageSubmissionRepository,
  GenericPageTemplateRepository,
  GenericPageTemplateTagRepository,
  GenericSessionRepository,
  GenericSessionWidgetRepository,
  GenericSessionLayoutRepository,
} from './repositories';
import {
  GenericSessionService,
  GenericWidgetService,
  GenericLayoutService,
  GenericQueueService,
  GenericRecoveryService,
  GenericCollaborationService,
} from './services';
import { GenericWebSocketGateway } from './gateways/generic-websocket.gateway';
import { GenericPageWorker } from './workers/generic-page.worker';
import {
  GenericSessionController,
  GenericWidgetController,
  GenericLayoutController,
  GenericQueueController,
  GenericCollaborationController,
} from './controllers';
import { GENERIC_PAGE_QUEUE } from './constants/generic-queue.constants';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GenericPage,
      GenericPageTemplate,
      GenericPageTemplateTag,
      GenericPageSubmission,
      GenericSession,
      GenericSessionWidget,
      GenericSessionLayout,
    ]),
    BullModule.registerQueue({
      name: GENERIC_PAGE_QUEUE,
    }),
    ScheduleModule.forRoot(),
    GenericAdminModule,
    GenericUserModule,
  ],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericPageTemplateTagRepository,
    GenericPageSubmissionRepository,
    GenericSessionRepository,
    GenericSessionWidgetRepository,
    GenericSessionLayoutRepository,
    GenericSessionService,
    GenericWidgetService,
    GenericLayoutService,
    GenericQueueService,
    GenericRecoveryService,
    GenericCollaborationService,
    GenericWebSocketGateway,
    GenericPageWorker,
  ],
  controllers: [
    GenericSessionController,
    GenericWidgetController,
    GenericLayoutController,
    GenericQueueController,
    GenericCollaborationController,
  ],
  exports: [GenericAdminModule, GenericUserModule, TypeOrmModule],
})
export class GenericModule {}
