import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  GenericPageAdminController,
  GenericPageTemplateAdminController,
} from './controllers';
import {
  GenericPageAdminService,
  GenericPageTemplateAdminService,
} from './services';
import {
  GenericPage,
  GenericPageTemplate,
  GenericPageTemplateTag,
  GenericPageSubmission,
} from '../entities';
import {
  GenericPageRepository,
  GenericPageTemplateRepository,
  GenericPageTemplateTagRepository,
  GenericPageSubmissionRepository,
} from '../repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GenericPage,
      GenericPageTemplate,
      GenericPageTemplateTag,
      GenericPageSubmission,
    ]),
  ],
  controllers: [GenericPageAdminController, GenericPageTemplateAdminController],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericPageTemplateTagRepository,
    GenericPageSubmissionRepository,
    GenericPageAdminService,
    GenericPageTemplateAdminService,
  ],
  exports: [GenericPageAdminService, GenericPageTemplateAdminService],
})
export class GenericAdminModule {}
