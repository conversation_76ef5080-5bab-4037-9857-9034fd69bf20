import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GenericPageUserController } from './controllers';
import { GenericPageUserService } from './services';
import {
  GenericPage,
  GenericPageTemplate,
  GenericPageSubmission,
} from '../entities';
import {
  GenericPageRepository,
  GenericPageTemplateRepository,
  GenericPageSubmissionRepository,
} from '../repositories';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GenericPage,
      GenericPageTemplate,
      GenericPageSubmission,
    ]),
  ],
  controllers: [GenericPageUserController],
  providers: [
    GenericPageRepository,
    GenericPageTemplateRepository,
    GenericPageSubmissionRepository,
    GenericPageUserService,
  ],
  exports: [GenericPageUserService],
})
export class GenericUserModule {}
